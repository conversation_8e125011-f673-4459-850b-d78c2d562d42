import 'dotenv/config';
import { db } from './packages/database/prisma/client';

async function checkCourseProducts() {
  try {
    console.log('Verificando produtos cadastrados...');
    
    const products = await db.courseProduct.findMany({
      include: {
        course: true
      }
    });
    
    console.log(`Total de produtos: ${products.length}`);
    
    if (products.length > 0) {
      console.log('\nProdutos encontrados:');
      products.forEach(product => {
        console.log(`- ID: ${product.caktoProductId}`);
        console.log(`  Curso: ${product.course.name}`);
        console.log(`  Nome do Produto: ${product.caktoProductName || 'N/A'}`);
        console.log('');
      });
    } else {
      console.log('\nNenhum produto encontrado. Criando produto de teste...');
      
      const courses = await db.courses.findMany();
      if (courses.length > 0) {
        const firstCourse = courses[0];
        console.log(`Criando produto para o curso: ${firstCourse.name}`);
        
        const newProduct = await db.courseProduct.create({
          data: {
            caktoProductId: 'cmdqkl4610002l504t2qxiqes',
            courseId: firstCourse.id,
            caktoProductName: 'Curso de Teste'
          },
          include: {
            course: true
          }
        });
        
        console.log('Produto criado:');
        console.log(`- ID: ${newProduct.caktoProductId}`);
        console.log(`- Curso: ${newProduct.course.name}`);
      } else {
        console.log('Nenhum curso encontrado.');
      }
    }
    
  } catch (error) {
    console.error('Erro:', error);
  } finally {
    await db.$disconnect();
  }
}

checkCourseProducts();