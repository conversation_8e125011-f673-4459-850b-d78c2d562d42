const fetch = require('node-fetch');

// Payload de teste para o webhook da Cakto
const webhookPayload = {
  "secret": "1340098d-340d-488a-af83-f80e0eaaa773",
  "event": "purchase_approved",
  "data": {
    "id": "test-purchase-" + Date.now(),
    "refId": "TEST123",
    "customer": {
      "name": "Usuário Teste",
      "email": "<EMAIL>",
      "phone": "11999999999",
      "docNumber": "12345678909"
    },
    "affiliate": "<EMAIL>",
    "offer": {
      "id": "OFFER123",
      "name": "Oferta Teste",
      "price": 100
    },
    "offer_type": "main",
    "product": {
      "name": "Curso de Teste",
      "id": "cmdqkl4610002l504t2qxiqes", // ID do produto/curso fornecido
      "short_id": "TEST123",
      "supportEmail": "<EMAIL>",
      "type": "unique",
      "invoiceDescription": "Curso de teste para webhook"
    },
    "parent_order": "ORDER123",
    "checkoutUrl": "https://pay.cakto.com.br/TEST",
    "status": "approved",
    "baseAmount": 100.0,
    "discount": 0.0,
    "amount": 100.0,
    "commissions": [
      {
        "user": "<EMAIL>",
        "totalAmount": 80.0,
        "type": "producer",
        "percentage": 80
      }
    ],
    "reason": null,
    "refund_reason": null,
    "installments": 1,
    "paymentMethod": "credit_card",
    "paymentMethodName": "Cartão de Crédito",
    "paidAt": new Date().toISOString(),
    "createdAt": new Date().toISOString(),
    "card": {
      "lastDigits": "1234",
      "holderName": "Teste Usuario",
      "brand": "visa"
    }
  }
};

async function testWebhook() {
  try {
    console.log('🚀 Testando webhook da Cakto...');
    console.log('📦 Payload:', JSON.stringify(webhookPayload, null, 2));
    
    const response = await fetch('http://localhost:3000/api/webhooks/cakto/purchase', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Cakto-Signature': 'test-signature' // Opcional, pois está comentado no código
      },
      body: JSON.stringify(webhookPayload)
    });
    
    const result = await response.text();
    
    console.log('📊 Status:', response.status);
    console.log('📄 Response:', result);
    
    if (response.ok) {
      console.log('✅ Webhook processado com sucesso!');
    } else {
      console.log('❌ Erro no webhook:', response.status);
    }
    
  } catch (error) {
    console.error('💥 Erro ao testar webhook:', error.message);
  }
}

// Executar o teste
testWebhook();